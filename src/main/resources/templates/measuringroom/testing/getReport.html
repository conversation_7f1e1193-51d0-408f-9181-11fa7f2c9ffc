<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        .hide {
            display: none !important;
        }

        /* 页面整体样式 - 简约设计 */
        #report {
            background: #ffffff;
        }

        /* 基本信息区域 - 限定在报告容器内 */
        #report .basic-info-section {
            background: #ffffff;
            border: 1px solid #e8ecf4;
            border-radius: 12px;
            margin-bottom: 32px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(114, 124, 245, 0.08);
        }

        #report .basic-info-header {
            background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);
            border-bottom: 1px solid #e8ecf4;
            color: #2c3e50;
            padding: 20px 24px;
            font-weight: 600;
            font-size: 16px;
            position: relative;
        }

        #report .basic-info-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(135deg, #727cf5 0%, #6c5ce7 100%);
        }

        #report .basic-info-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        #report .basic-info-header i {
            margin-right: 8px;
            color: #727cf5;
            font-size: 14px;
        }

        #report .basic-info-content {
            padding: 32px 28px;
            background: white;
        }

        #report .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 24px 32px;
        }

        #report .info-item {
            text-align: center;
            padding: 24px 20px;
            background: #f8f9ff;
            border-radius: 8px;
            border-top: 3px solid #727cf5;
            transition: all 0.3s ease;
            position: relative;
        }

        #report .info-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(114, 124, 245, 0.15);
            border-top-color: #6c5ce7;
        }

        #report .info-label {
            font-size: 14px;
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        #report .info-value {
            font-size: 18px;
            color: #2c3e50;
            font-weight: 600;
            line-height: 1.3;
            word-break: break-all;
        }

        /* 为不同信息项添加不同的顶部边框颜色 */
        #report .info-item:nth-child(1) { border-top-color: #727cf5; }
        #report .info-item:nth-child(2) { border-top-color: #f093fb; }
        #report .info-item:nth-child(3) { border-top-color: #4facfe; }
        #report .info-item:nth-child(4) { border-top-color: #43e97b; }
        #report .info-item:nth-child(5) { border-top-color: #fa709a; }

        #report .info-item:nth-child(1):hover { border-top-color: #6c5ce7; }
        #report .info-item:nth-child(2):hover { border-top-color: #f5576c; }
        #report .info-item:nth-child(3):hover { border-top-color: #00f2fe; }
        #report .info-item:nth-child(4):hover { border-top-color: #38f9d7; }
        #report .info-item:nth-child(5):hover { border-top-color: #fee140; }

        /* 打印和下载按钮样式 - 限定作用范围 */
        .card-widgets {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 10;
        }

        .card-widgets .dropdown-toggle {
            background: #727cf5;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 16px;
            box-shadow: 0 2px 8px rgba(114, 124, 245, 0.3);
            transition: all 0.3s ease;
        }

        .card-widgets .dropdown-toggle:hover {
            background: #6c5ce7;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(114, 124, 245, 0.4);
            color: white;
            text-decoration: none;
        }

        .card-widgets .dropdown-menu {
            border: none;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            padding: 8px 0;
            margin-top: 8px;
        }

        .card-widgets .dropdown-item {
            padding: 10px 20px;
            font-size: 14px;
            color: #2c3e50;
            transition: all 0.2s ease;
        }

        .card-widgets .dropdown-item:hover {
            background: #f8f9ff;
            color: #727cf5;
        }

        .card-widgets .dropdown-item i {
            color: #727cf5;
        }

        /* 图表容器样式 */
        .charts-container {
            overflow: hidden;
            width: 100%;
            max-width: 100%;
        }

        /* 为柱状图添加额外的上边距 */
        .chart-item.column-chart {
            margin-top: 24px;
        }

        /* 图表响应式处理 */
        .chart-item .highcharts-container {
            width: 100% !important;
            max-width: 100% !important;
            overflow: hidden !important;
        }

        .chart-item > div {
            overflow: hidden !important;
            width: 100% !important;
            max-width: 100% !important;
            position: relative;
        }

        .chart-item canvas,
        .chart-item > div {
            max-width: 100% !important;
            width: 100% !important;
            height: auto !important;
            overflow: hidden;
            margin: 0 auto;
            box-sizing: border-box;
        }

        /* 图表容器通用样式 */
        [id^="chartContainer"] {
            width: 100% !important;
            max-width: 100% !important;
            height: auto !important;
            max-height: 400px !important;
            margin: 0 auto;
            display: block;
            overflow: hidden;
            position: relative;
        }

        /* 图表画布统一样式 */
        [id^="chartCanvas"] {
            width: 100% !important;
            max-width: 100% !important;
            height: auto !important;
            max-height: 400px !important;
            margin: 0 auto;
            display: block;
            overflow: hidden;
            box-sizing: border-box;
        }

        /* 表格样式 - 限定在报告容器内 */
        #report .table-responsive {
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid #e8ecf4;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(114, 124, 245, 0.05);
        }

        #report .table {
            margin-bottom: 0;
            font-size: 14px;
            border-collapse: separate;
            border-spacing: 0;
        }

        #report .table th {
            background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);
            color: #2c3e50;
            font-weight: 600;
            border: none;
            border-bottom: 1px solid #e8ecf4;
            padding: 16px 20px;
            font-size: 14px;
            position: relative;
        }

        #report .table th:first-child {
            border-top-left-radius: 10px;
        }

        #report .table th:last-child {
            border-top-right-radius: 10px;
        }

        #report .table th i {
            color: #727cf5;
            font-size: 13px;
            margin-right: 6px;
        }

        #report .table td {
            padding: 16px 20px;
            vertical-align: middle;
            border-top: 1px solid #f1f5f9;
            background: #fff;
            color: #2c3e50;
            line-height: 1.5;
            transition: all 0.2s ease;
        }

        #report .table-striped tbody tr:nth-of-type(odd) td {
            background: #f8f9ff;
        }

        #report .table tbody tr:hover td {
            background: #f1f3ff;
            transform: translateX(2px);
        }

        /* 因子层级样式 - 限定在报告容器内 */
        #report .factor-level {
            margin-bottom: 32px;
            border: none;
            border-radius: 12px;
            overflow: hidden;
            background: #fff;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
        }

        #report .factor-level:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
        }

        #report .factor-level::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(114, 124, 245, 0.02) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        #report .factor-level:hover::before {
            opacity: 1;
        }

        #report .factor-level-header {
            background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);
            border-bottom: 1px solid #e8ecf4;
            color: #2c3e50;
            padding: 20px 24px;
            font-weight: 600;
            font-size: 16px;
            position: relative;
        }

        #report .factor-level-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(135deg, #727cf5 0%, #6c5ce7 100%);
        }

        #report .factor-level-header i {
            margin-right: 8px;
            color: #727cf5;
            font-size: 14px;
        }

        #report .factor-level-content {
            padding: 24px;
            background: #fff;
        }

        #report .factor-section {
            margin-bottom: 24px;
            padding-bottom: 0;
            border-bottom: none;
        }

        #report .factor-section:last-child {
            margin-bottom: 0;
        }

        #report .factor-main-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #727cf5;
            position: relative;
        }

        #report .factor-main-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 60px;
            height: 2px;
            background: linear-gradient(90deg, #727cf5, #6c5ce7);
        }

        #report .factor-sub-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 16px;
            padding-left: 12px;
            border-left: 3px solid #727cf5;
            font-size: 14px;
        }

        #report .factor-charts-section {
            margin-top: 24px;
            padding: 24px;
            background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);
            border-radius: 10px;
            border: 1px solid #e8ecf4;
            position: relative;
        }

        #report .factor-charts-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #727cf5, #6c5ce7);
            border-radius: 10px 10px 0 0;
        }

        #report .factor-charts-section h5 {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        #report .factor-charts-section h5 i {
            margin-right: 8px;
            color: #727cf5;
        }

        /* 图表容器样式 - 限定在报告容器内 */
        #report .chart-item {
            margin-bottom: 24px;
            overflow: hidden;
            background: #ffffff;
            border: 1px solid #e8ecf4;
            padding: 24px;
            width: 100%;
            max-width: 100%;
            text-align: center;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(114, 124, 245, 0.05);
            transition: all 0.3s ease;
        }

        #report .chart-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(114, 124, 245, 0.15);
        }

        /* 报告头部背景样式 */
        .report-header-bg {
            position: relative;
            min-height: 300px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: local;
            margin: -20px -20px 30px -20px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            border-radius: 0 0 20px 20px;
        }

        .bg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                180deg,
                rgba(0, 0, 0, 0.1) 0%,
                rgba(0, 0, 0, 0.05) 30%,
                rgba(0, 0, 0, 0.2) 70%,
                rgba(0, 0, 0, 0.6) 100%
            );
        }

        .header-content {
            position: relative;
            z-index: 3;
            text-align: center;
            padding: 20px 40px 35px 40px;
            width: 100%;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.05) 100%
            );
            backdrop-filter: blur(15px) saturate(1.2);
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            margin-top: 100px;
        }

        .header-content h4 {
            text-shadow:
                0 3px 6px rgba(0, 0, 0, 0.9),
                0 1px 3px rgba(0, 0, 0, 0.7),
                0 0 20px rgba(255, 255, 255, 0.4);
            font-size: 32px;
            margin-bottom: 0;
            color: #ffffff !important;
            font-weight: 700;
            letter-spacing: 2px;
            text-transform: uppercase;
            position: relative;
        }

        .header-content h4::before {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, transparent, #ffffff, transparent);
            border-radius: 2px;
        }

        /* 普通标题样式 - 添加主色调 */
        #normalHeader {
            background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);
            border-bottom: 3px solid #727cf5;
            margin: -20px -20px 32px -20px;
            padding: 32px 20px;
            position: relative;
        }

        #normalHeader::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #727cf5, #6c5ce7);
        }

        #normalHeader h4 {
            color: #2c3e50 !important;
            margin: 0;
            font-weight: 600;
            font-size: 24px;
            text-align: center;
            position: relative;
        }

        /* 响应式设计 - 限定在报告容器内 */
        @media (max-width: 992px) {
            #report .info-grid {
                grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
                gap: 20px 24px;
            }
        }

        @media (max-width: 768px) {
            .report-header-bg {
                min-height: 230px;
                margin: -15px -15px 24px -15px;
            }

            .header-content {
                padding: 18px 20px 30px 20px;
                margin-top: 80px;
            }

            .header-content h4 {
                font-size: 24px;
                letter-spacing: 1.5px;
            }

            #normalHeader {
                margin: -15px -15px 24px -15px;
                padding: 24px 15px;
            }

            #report .info-grid {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                gap: 16px 20px;
            }

            #report .basic-info-content {
                padding: 24px 20px;
            }

            #report .info-item {
                padding: 20px 16px;
            }

            #report .info-value {
                font-size: 16px;
            }

            #report .factor-level-content {
                padding: 20px;
            }

            #report .factor-level-header {
                padding: 16px 20px;
                font-size: 15px;
            }

            #report .table th, #report .table td {
                padding: 12px 16px;
                font-size: 13px;
            }

            #report .chart-item {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .report-header-bg {
                min-height: 200px;
                margin: -10px -10px 20px -10px;
            }

            .header-content {
                padding: 15px 15px 25px 15px;
                margin-top: 60px;
            }

            .header-content h4 {
                font-size: 20px;
                letter-spacing: 1px;
            }

            .header-content h4::before {
                width: 40px;
                height: 2px;
            }

            #normalHeader {
                margin: -10px -10px 20px -10px;
                padding: 20px 10px;
            }

            #normalHeader h4 {
                font-size: 20px;
            }

            #report .basic-info-header {
                padding: 20px;
            }

            #report .basic-info-header h4 {
                font-size: 16px;
            }

            #report .basic-info-content {
                padding: 20px 16px;
            }

            #report .info-grid {
                grid-template-columns: 1fr 1fr;
                gap: 16px;
            }

            #report .info-item {
                padding: 16px 12px;
            }

            #report .info-label {
                font-size: 12px;
                margin-bottom: 6px;
            }

            #report .info-value {
                font-size: 14px;
            }

            #report .factor-level-header {
                padding: 16px;
                font-size: 14px;
            }

            #report .factor-level-content {
                padding: 16px;
            }

            #report .table th, #report .table td {
                padding: 10px 12px;
                font-size: 12px;
            }

            #report .chart-item {
                padding: 16px;
            }

            #report .factor-charts-section {
                padding: 16px;
            }

            #report .factor-charts-section h5 {
                font-size: 13px;
            }
        }

        /* 微妙的动画效果 - 限定在报告容器内 */
        #report .basic-info-section {
            animation: fadeInUp 0.6s ease-out;
        }

        #report .factor-level {
            animation: fadeInUp 0.6s ease-out;
        }

        #report .factor-level:nth-child(2) { animation-delay: 0.1s; }
        #report .factor-level:nth-child(3) { animation-delay: 0.2s; }
        #report .factor-level:nth-child(4) { animation-delay: 0.3s; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 打印样式 */
        @media print {
            #report .basic-info-section,
            #report .factor-level {
                border: 1px solid #ddd !important;
                box-shadow: none !important;
                animation: none !important;
            }

            #report .basic-info-header,
            #report .factor-level-header {
                background: #f5f5f5 !important;
                color: #333 !important;
            }

            #report .basic-info-section {
                background: #fff !important;
            }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row d-print-none">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">心理测试</a></li>
                    </ol>
                </div>
                <h4 class="page-title">心理测试</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card pt-2">
                <div class="card-body ml-4 mr-4" id="report">
                    <div class="d-print-none">
                        <div class="dropdown card-widgets">
                            <a href="#" class="dropdown-toggle arrow-none" data-toggle="dropdown" aria-expanded="false">
                                <i class="fa fa-ellipsis-v"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right">
                                <a href="javascript:window.print()" class="dropdown-item"><i class="fa fa-print mr-2"></i>打印</a>
                                <a href="javascript:download()" class="dropdown-item"><i class="fa fa-download mr-2"></i>下载报告[word]</a>
                            </div>
                        </div>
                    </div>
                    <!-- 报告头部背景区域 -->
                    <div class="report-header-bg" id="reportHeaderBg" style="display: none;">
                        <div class="bg-overlay"></div>
                        <div class="header-content">
                            <div class="text-center">
                                <h4 class="m-0 letter-spacing-2 font-weight-500 text-white">《<span class="scaleName"></span>》测评报告</h4>
                            </div>
                        </div>
                    </div>

                    <!-- 普通标题区域 -->
                    <div class="clearfix" id="normalHeader">
                        <div class="text-center mt-4 mb-4">
                            <h4 class="m-0 letter-spacing-2 font-weight-500">《<span class="scaleName"></span>》测评报告</h4>
                        </div>
                    </div>
                    
                    <!-- 基本信息 -->
                    <div class="basic-info-section">
                        <div class="basic-info-header">
                            <i class="fa fa-user-circle mr-2"></i>基本信息
                        </div>
                        <div class="basic-info-content">
                            <div class="info-grid">
                                <div class="info-item">
                                    <div class="info-label">姓名</div>
                                    <div class="info-value" id="realName"></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">测试日期</div>
                                    <div class="info-value" id="startDate"></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">所属组织</div>
                                    <div class="info-value" id="fullStructName"></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">耗时</div>
                                    <div class="info-value" id="costTime"></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">测试项目</div>
                                    <div class="info-value scaleName"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 因子结果分析区域 -->
                    <div id="factorAnalysisSection"></div>

                </div>
            </div> <!-- end card -->
            <div class="alert alert-light m-2 hide" role="alert">

            </div>
        </div> <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts-more.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/solid-gauge.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/exporting.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/export-data.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/rgbcolor.min.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/stackblur.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/canvg/canvg.js}"></script>
    <script th:src="@{/static/js/pages/chartDataConfig.js}"></script>
    <script type="text/javascript">
        let recordId = getUrlParam('recordId');
        let reportData, testRecord, scale, user;
        let chartsImgArray = []; // 图表图片数组

        let initReport = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.2, time: false
            });
            $.ajax({
                type: "POST",
                url: "/measuringroom/testing/report?recordId=" + recordId,
                dataType: "json",
                contentType: "application/json",
                async: false,
                success: function (data) {
                    layer.closeAll();
                    let res = JSON.parse(data);
                    if (res.resultCode !== undefined) {
                        if (res.resultCode === 201) {
                            $("#report").hide();
                            $(".alert").removeClass("hide").addClass("show");
                            $(".alert").append('<img class="mr-1" src="/static/images/success.png" width="32" />您已经完成测试！');
                            return;
                        } else if (res.resultCode !== 200) {
                            layer.msg(res.resultMsg || '获取报告失败', { icon: 2, time: 2000 });
                            return;
                        }
                    }
                    
                    if (!res.data || !res.data.testRecord) {
                        layer.msg('报告数据格式错误', { icon: 2, time: 2000 });
                        return;
                    }
                    
                    reportData = res.data;
                    testRecord = res.data.testRecord;
                    scale = res.data.testRecord.scale;
                    user = res.data.testRecord.user;
                    
                    // 数据加载完成后初始化页面
                    getBaseInfo();
                    processReport();
                },
                error: function(xhr, status, error) {
                    layer.closeAll();
                    layer.msg('网络错误，请稍后重试', { icon: 2, time: 2000 });
                }
            });
        };
        
        let getBaseInfo = function () {
            let userName = user.realName === "" ? user.loginName : user.realName;
            let formattedDate = moment(testRecord.startTime).format("YYYY-MM-DD");
            let formattedTime = formatSeconds(testRecord.timeInterval);

            // 填充基本信息
            $(".scaleName").html(scale.scaleName);
            $("#realName").html(userName);
            $("#fullStructName").html(user.structName);
            $("#startDate").html(formattedDate);
            $("#costTime").html(formattedTime);
        };

        // 统一的因子数据处理函数
        let processFactorHierarchy = function(factorList) {
            if (!factorList || factorList.length === 0) {
                return { topLevel: [], parentGroups: [], independentFactors: [] };
            }

            let topLevelFactors = [];
            let parentGroups = [];
            let independentFactors = [];
            let processedFactorIds = new Set();

            // 递归收集因子的通用函数
            let collectFactors = function(factors, parentInfo) {
                $.each(factors, function(index, factorData) {
                    if (processedFactorIds.has(factorData.factorId)) return;
                    processedFactorIds.add(factorData.factorId);

                    if (factorData.children && factorData.children.length > 0) {
                        collectFactors(factorData.children, {
                            parentId: factorData.factorId,
                            parentName: factorData.factorName
                        });
                    } else {
                        if (parentInfo && factorData.factorType === 1) {
                            let existingGroup = parentGroups.find(group => group.parentId === parentInfo.parentId);
                            if (existingGroup) {
                                if (!existingGroup.children.some(child => child.factorId === factorData.factorId)) {
                                    existingGroup.children.push(factorData);
                                }
                            } else {
                                parentGroups.push({
                                    parentId: parentInfo.parentId,
                                    parentName: parentInfo.parentName,
                                    children: [factorData]
                                });
                            }
                        } else {
                            if (!independentFactors.some(factor => factor.factorId === factorData.factorId)) {
                                independentFactors.push(factorData);
                            }
                        }
                    }
                });
            };

            // 处理顶层因子和直接子因子
            $.each(factorList, function(index, factorData) {
                if (factorData.children && factorData.children.length > 0) {
                    topLevelFactors.push(factorData);
                    parentGroups.push({
                        parentId: factorData.factorId,
                        parentName: factorData.factorName,
                        children: factorData.children
                    });
                } else if (!processedFactorIds.has(factorData.factorId)) {
                    independentFactors.push(factorData);
                    processedFactorIds.add(factorData.factorId);
                }
            });

            collectFactors(factorList, null);
            return { topLevel: topLevelFactors, parentGroups: parentGroups, independentFactors: independentFactors };
        };

        // 通用的内容生成函数
        let generateFactorContent = function(factors, config) {
            if (!factors || factors.length === 0) return "";

            const { title, icon = 'fa-list', chartPrefix, isTopLevel = false } = config;

            let content = `<div class='factor-level'>
                <div class='factor-level-header'>${icon ? `<i class='fa ${icon} mr-2'></i>` : ''}${title}</div>
                <div class='factor-level-content'>
                    <div class='factor-section'>
                        <div class='table-responsive'>
                            <table class='table table-striped'>
                                <thead>
                                    <tr><th class='text-left'><i class='fa fa-tag'></i>因子名称</th><th class='text-left'><i class='fa fa-file-text-o'></i>结果解释</th></tr>
                                </thead>
                                <tbody>`;

            $.each(factors, function(index, factorData) {
                content += `<tr><td class='text-left'>${factorData.factorName}</td><td class='text-left'>${factorData.interpretation || "暂无解释内容"}</td></tr>`;
            });

            content += `</tbody></table></div></div>
                <div class='factor-charts-section'>
                    <h5><i class='fa fa-bar-chart mr-2'></i>图表分析</h5>`;

            content += generateChartContainers(factors, chartPrefix);
            content += `</div></div></div>`;

            return content;
        };

        // 生成图表容器的通用函数
        let generateChartContainers = function(factors, chartPrefix) {
            let content = "";
            const chartStyle = "width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;";

            if (scale.listCharts && scale.listCharts.length > 0) {
                content += `<div class='chart-item' id='chartContainer_${chartPrefix}'>
                    <canvas id='chartCanvas_${chartPrefix}' style='${chartStyle}'></canvas>
                </div>`;
            } else {
                if (factors.length === 1) {
                    content += `<div class='chart-item' id='chartContainer_${chartPrefix}_column'>
                        <canvas id='chartCanvas_${chartPrefix}_column' style='${chartStyle}'></canvas>
                    </div>`;
                } else {
                    content += `<div class='chart-item' id='chartContainer_${chartPrefix}_line'>
                        <canvas id='chartCanvas_${chartPrefix}_line' style='${chartStyle}'></canvas>
                    </div>
                    <div class='chart-item column-chart' id='chartContainer_${chartPrefix}_column'>
                        <canvas id='chartCanvas_${chartPrefix}_column' style='${chartStyle}'></canvas>
                    </div>`;
                }
            }
            return content;
        };

        // 简化的内容生成函数
        let generateTopLevelContent = function(topLevelFactors) {
            return generateFactorContent(topLevelFactors, {
                title: '结果解释及建议',
                icon: 'fa-lightbulb-o',
                chartPrefix: 'topLevel',
                isTopLevel: true
            });
        };

        let generateParentGroupContent = function(parentGroup) {
            return generateFactorContent(parentGroup.children, {
                title: parentGroup.parentName,
                icon: 'fa-folder-open',
                chartPrefix: `parent_${parentGroup.parentId}`
            });
        };

        let generateIndependentFactorsContent = function(independentFactors) {
            return generateFactorContent(independentFactors, {
                title: '结果解释及建议',
                icon: 'fa-lightbulb-o',
                chartPrefix: 'independent'
            });
        };

        let processReport = function () {
            let content = "";
            if (reportData.listExplains && reportData.listExplains.length > 0) {
                let organizedData = processFactorHierarchy(reportData.listExplains);
                content += generateTopLevelContent(organizedData.topLevel);
                $.each(organizedData.parentGroups, function(index, parentGroup) {
                    content += generateParentGroupContent(parentGroup);
                });
                content += generateIndependentFactorsContent(organizedData.independentFactors);
            } else {
                content += "<div class='alert alert-info'><i class='fa fa-info-circle mr-2'></i>暂无数据</div>";
            }

            $("#factorAnalysisSection").html(content);
            createCharts();
        };

        let createCharts = function() {
            if (!reportData.listExplains || reportData.listExplains.length === 0) return;

            // 调试：打印数据结构
            console.log('reportData.listExplains:', reportData.listExplains);

            let organizedData = processFactorHierarchy(reportData.listExplains);

            if (organizedData.topLevel.length > 0) {
                createChart(organizedData.topLevel, 'topLevel');
            }

            $.each(organizedData.parentGroups, function(index, parentGroup) {
                createChart(parentGroup.children, `parent_${parentGroup.parentId}`);
            });

            if (organizedData.independentFactors.length > 0) {
                createChart(organizedData.independentFactors, 'independent');
            }
        };

        // 统一的图表创建函数
        let createChart = function(factors, chartPrefix) {
            if (!factors || factors.length === 0) return;

            if (scale.listCharts && scale.listCharts.length > 0) {
                createConfiguredChart(factors, chartPrefix);
            } else {
                createDefaultCharts(factors, chartPrefix);
            }
        };

        // 创建配置的图表
        let createConfiguredChart = function(factors, chartPrefix) {
            let containerId = `chartCanvas_${chartPrefix}`;
            let containerElement = document.getElementById(containerId);

            if (!containerElement) return;

            try {
                let chartConfig = scale.listCharts[0];
                if (!chartDataConfig[chartConfig.chartType]) {
                    console.warn('未找到图表配置:', chartConfig.chartType);
                    return;
                }

                let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));
                let parentContainer = containerElement.parentElement;
                let containerWidth = (parentContainer.offsetWidth || 400) - 30;
                let containerHeight = Math.min(350, containerElement.offsetHeight || 350);

                chartOptions.chart.width = containerWidth;
                chartOptions.chart.height = containerHeight;
                chartOptions.chart.renderTo = containerId;
                chartOptions.chart.spacing = [10, 10, 10, 10];
                chartOptions.title.text = "";

                // 调试：打印因子数据
                console.log('factors for chart:', factors);

                if(chartOptions.chart.type === 'pie') {
                    let yData = factors.map((factorData, index) => {
                        let score = parseFloat(factorData.score) || 0;
                        console.log(`因子 ${factorData.factorName} 得分: ${score}`);
                        return {
                            name: factorData.factorName,
                            y: score,
                            selected: index === 0
                        };
                    });
                    chartOptions.series.push({
                        name: '因子分',
                        colorByPoint: true,
                        data: yData
                    });
                } else {
                    let categories = factors.map(f => f.factorName);
                    let data = factors.map(f => {
                        let score = parseFloat(f.score) || 0;
                        console.log(`因子 ${f.factorName} 得分: ${score}`);
                        return score;
                    });

                    if (chartOptions.xAxis) {
                        chartOptions.xAxis.categories = categories;
                    }
                    chartOptions.series.push({
                        name: '因子分',
                        data: data,
                        color: "#ffbc00"
                    });
                }

                let chart = new Highcharts.Chart(chartOptions);

                setTimeout(() => {
                    if (chart && chart.reflow) chart.reflow();
                    if (chart && chart.setSize) {
                        let maxHeight = Math.min(400, containerElement.offsetHeight || 400);
                        let maxWidth = containerElement.offsetWidth || 400;
                        chart.setSize(maxWidth, maxHeight, false);
                    }
                }, 100);

                try {
                    let canvasId = "#" + containerId;
                    let charData = $(canvasId).highcharts().getSVG();
                    canvg(containerId, charData);
                    let chartsImg = $(canvasId)[0].toDataURL("image/png");
                    chartsImgArray.push(chartsImg);
                } catch(e) {
                    console.log('图表图片保存失败:', e);
                }
            } catch(e) {
                console.error('创建图表失败:', e);
            }
        };

        // 优化的默认图表创建函数
        let createDefaultCharts = function(factors, chartPrefix) {
            if (!factors || factors.length === 0) return;

            let categories = factors.map(f => f.factorName);
            let data = factors.map(f => parseFloat(f.score) || 0);

            if (factors.length === 1) {
                createSingleColumnChart(factors[0], chartPrefix);
            } else {
                createLineAndColumnCharts(factors, chartPrefix, categories, data);
            }
        };

        // 创建单个因子柱状图
        let createSingleColumnChart = function(factor, chartPrefix) {
            let containerId = `chartCanvas_${chartPrefix}_column`;
            let containerElement = document.getElementById(containerId);

            if (!containerElement) return;

            try {
                let columnChartOptions = JSON.parse(JSON.stringify(chartDataConfig.column));
                let parentContainer = containerElement.parentElement;
                let containerWidth = (parentContainer.offsetWidth || 400) - 30;
                let containerHeight = Math.min(350, containerElement.offsetHeight || 350);

                Object.assign(columnChartOptions.chart, {
                    width: containerWidth,
                    height: containerHeight,
                    renderTo: containerId,
                    spacing: [10, 10, 10, 10]
                });

                columnChartOptions.title.text = "";
                columnChartOptions.xAxis.categories = [factor.factorName];
                columnChartOptions.series.push({
                    name: '因子分',
                    data: [parseFloat(factor.score) || 0],
                    color: "#ffbc00"
                });

                let chart = new Highcharts.Chart(columnChartOptions);

                setTimeout(() => {
                    if (chart && chart.reflow) chart.reflow();
                    if (chart && chart.setSize) {
                        let maxHeight = Math.min(400, containerElement.offsetHeight || 400);
                        let maxWidth = containerElement.offsetWidth || 400;
                        chart.setSize(maxWidth, maxHeight, false);
                    }
                }, 100);

                saveChartImage(containerId);
            } catch(e) {
                console.error('创建柱状图失败:', e);
            }
        };

        // 创建折线图和柱状图（多个因子）
        let createLineAndColumnCharts = function(factors, chartPrefix, categories, data) {
            createLineChart(factors, chartPrefix, categories, data);
            createColumnChart(factors, chartPrefix, categories, data);
        };

        // 创建折线图
        let createLineChart = function(factors, chartPrefix, categories, data) {
            let containerId = `chartCanvas_${chartPrefix}_line`;
            let containerElement = document.getElementById(containerId);

            if (!containerElement) return;

            try {
                let lineChartOptions = JSON.parse(JSON.stringify(chartDataConfig.line));
                setupChartOptions(lineChartOptions, containerId, containerElement);

                lineChartOptions.xAxis.categories = categories;
                lineChartOptions.series.push({
                    name: '因子分',
                    data: data,
                    color: "#ffbc00"
                });

                let chart = new Highcharts.Chart(lineChartOptions);
                setupChartResize(chart, containerElement);
                saveChartImage(containerId);
            } catch(e) {
                console.error('创建折线图失败:', e);
            }
        };

        // 创建柱状图
        let createColumnChart = function(factors, chartPrefix, categories, data) {
            let containerId = `chartCanvas_${chartPrefix}_column`;
            let containerElement = document.getElementById(containerId);

            if (!containerElement) return;

            try {
                let columnChartOptions = JSON.parse(JSON.stringify(chartDataConfig.column));
                setupChartOptions(columnChartOptions, containerId, containerElement);

                columnChartOptions.xAxis.categories = categories;
                columnChartOptions.series.push({
                    name: '因子分',
                    data: data,
                    color: "#ffbc00"
                });

                let chart = new Highcharts.Chart(columnChartOptions);
                setupChartResize(chart, containerElement);
                saveChartImage(containerId);
            } catch(e) {
                console.error('创建柱状图失败:', e);
            }
        };

        // 通用的图表配置设置函数
        let setupChartOptions = function(chartOptions, containerId, containerElement) {
            let parentContainer = containerElement.parentElement;
            let containerWidth = (parentContainer.offsetWidth || 400) - 30;
            let containerHeight = Math.min(350, containerElement.offsetHeight || 350);

            Object.assign(chartOptions.chart, {
                width: containerWidth,
                height: containerHeight,
                renderTo: containerId,
                spacing: [10, 10, 10, 10]
            });
            chartOptions.title.text = "";
        };

        // 通用的图表尺寸调整函数
        let setupChartResize = function(chart, containerElement) {
            setTimeout(() => {
                if (chart && chart.reflow) chart.reflow();
                if (chart && chart.setSize) {
                    let maxHeight = Math.min(400, containerElement.offsetHeight || 400);
                    let maxWidth = containerElement.offsetWidth || 400;
                    chart.setSize(maxWidth, maxHeight, false);
                }
            }, 100);
        };

        // 通用的图表图片保存函数
        let saveChartImage = function(containerId) {
            try {
                let canvasId = "#" + containerId;
                let charData = $(canvasId).highcharts().getSVG();
                canvg(containerId, charData);
                let chartsImg = $(canvasId)[0].toDataURL("image/png");
                chartsImgArray.push(chartsImg);
            } catch(e) {
                console.log('图表图片保存失败:', e);
            }
        };



        let saveCharts = function () {
            let jsonObj = {};
            jsonObj.recordId = recordId;
            jsonObj.chartsImg = chartsImgArray;
            $.ajax({
                type: 'POST',
                url: '/measuringroom/testing/save_report_charts',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {

                }
            });
        };
        
        let download = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.2, time: false
            });
            $.post("/export/test_report_word", { "recordId": recordId }, function (res) {
                layer.closeAll();
                if(res.resultCode ===200) {
                    location.href="/static/upload/"+res.resultMsg;
                }
                else {
                    layer.msg('下载失败!',{ icon: 2, time: 2000 });
                }
            }, 'json');
        };

        // 检查并设置背景图片
        function checkAndSetBackground() {
            $.ajax({
                url: "/measuringroom/testing/get_background",
                type: 'POST',
                data: { recordId: recordId },
                dataType: "JSON",
                success: function (res) {
                    if (res.resultCode === 200 && res.data && res.data.showBackground && res.data.backgroundUrl) {
                        $("#reportHeaderBg").css("background-image", "url('" + res.data.backgroundUrl + "')").show();
                        $("#normalHeader").hide();
                    } else {
                        $("#reportHeaderBg").hide();
                        $("#normalHeader").show();
                    }
                },
                error: function () {
                    $("#reportHeaderBg").hide();
                    $("#normalHeader").show();
                }
            });
        }

        $(function () {
            initReport();
            checkAndSetBackground();

            if (getUrlParam("savecharts") === 'true') {
                saveCharts();
            }

            // 图表响应式处理
            setTimeout(function() {
                if (typeof Highcharts !== 'undefined') {
                    Highcharts.charts.forEach(function(chart) {
                        if (chart) {
                            // 重新设置图表尺寸以适应容器
                            let parentContainer = chart.container.parentElement;
                            let chartHeight = Math.min(350, chart.container.offsetHeight || 350);
                            let chartWidth = (parentContainer.offsetWidth || chart.container.offsetWidth) - 30;
                            
                            if (chart.options.chart.type === 'gauge' || chart.options.chart.type === 'solidgauge') {
                                // 仪表图特殊处理
                                chart.update({
                                    chart: {
                                        spacing: [5, 5, 5, 5]
                                    },
                                    pane: {
                                        size: '90%',
                                        center: ['50%', '70%']
                                    }
                                });
                            } else {
                                // 其他图表类型
                                chart.setSize(chartWidth, chartHeight, false);
                            }
                            chart.reflow();
                        }
                    });
                }
            }, 1000);

            // 窗口大小改变时重新调整图表
            $(window).on('resize', function() {
                setTimeout(function() {
                    if (typeof Highcharts !== 'undefined') {
                        Highcharts.charts.forEach(function(chart) {
                            if (chart) {
                                let chartHeight = Math.min(400, chart.container.offsetHeight || 400);
                                let chartWidth = chart.chartWidth || chart.container.offsetWidth;
                                chart.setSize(chartWidth, chartHeight, false);
                                chart.reflow();
                            }
                        });
                    }
                }, 300);
            });
        });
    </script>
</th:block>
</body>
</html>